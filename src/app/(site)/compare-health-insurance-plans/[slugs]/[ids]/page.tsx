import axios from "axios";
import React from "react";
import ComparisonPage from "@/components/Compare";
import { getPopularPlansData } from "@/components/Compare/data/popularPlansData";
import { formatPlanVariantName } from "@/utils/planVariantFormatter";
import BreadcrumbsSchema from "@/components/SchemaMarkup/Breadcrumbs";
import FaqSchema from "@/components/SchemaMarkup/FaqSchema";

// Dynamic runtime configuration for API calls
export const dynamic = "force-dynamic";
export const dynamicParams = true;
export const revalidate = 0; // Disable caching for real-time data
export const fetchCache = "force-no-store"; // Force fresh data on each request

// Configure axios with timeout and retry logic
const apiClient = axios.create({
  timeout: 10000, // 10 second timeout
  headers: {
    "Content-Type": "application/json",
  },
});

// Add request interceptor for logging
apiClient.interceptors.request.use(
  (config) => {
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export async function generateMetadata({
  params,
}: {
  params: { slugs: string; ids: string };
}) {
  try {
    // Split the IDs by "-" to get individual IDs
    const individualIds = params.ids.split("-");

    if (individualIds.length !== 2) {
      throw new Error(
        "Invalid ID format. Expected exactly two IDs to compare."
      );
    }

    // Fetch company data to get actual plan names
    let plan1Name = "";
    let plan2Name = "";
    let insurer1Name = "";
    let insurer2Name = "";

    try {
      const data = await fetchCompanyData(individualIds);

      if (
        data?.data?.health_product_variants &&
        data.data.health_product_variants.length >= 2
      ) {
        // Extract plan names from the API response
        const plan1 = formatPlanVariantName(
          data.data.health_product_variants[0].product.name,
          data.data.health_product_variants[0].variant_name
        );
        const plan2 = formatPlanVariantName(
          data.data.health_product_variants[1].product.name,
          data.data.health_product_variants[1].variant_name
        );

        // Get plan names - try different possible fields
        plan1Name = plan1;
        plan2Name = plan2;
        insurer1Name =
          data.data.health_product_variants[0].product.insurer.name;
        insurer2Name =
          data.data.health_product_variants[1].product.insurer.name;
      }
    } catch (error) {
      // If API fails, fall back to slug-based names
      const individualSlugs = params.slugs.split("-vs-");
      const [first, second] = individualSlugs;

      const formatCompanyName = (slug: string) => {
        return slug
          .split("-")
          .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ");
      };

      plan1Name = formatCompanyName(first);
      plan2Name = formatCompanyName(second);
    }

    const title = `Compare ${insurer1Name} ${plan1Name} vs ${insurer2Name} ${plan2Name} Health Insurance Plan - Oneassure`;
    const description = `Compare ${insurer1Name} ${plan1Name} Vs ${insurer2Name} ${plan2Name} Health Insurance Plans at Oneassure.in . Get the best coverage, benefits, premiums, topup plans & more tailored to your needs for Family, Indivisuals & Senior Citizen.`;
    const imageUrl =
      "https://cdn.oasr.in/oa-site/cms-uploads/media/Clip_path_group_beb53ec1ee.svg";
    const pageUrl = `${process.env.NEXT_PUBLIC_BASE_URL}/compare-health-insurance-plans/${params.slugs}/${params.ids}`;

    // Comparison keywords for SEO
    const comparisonKeywords = [
      `Compare ${plan1Name} ${plan2Name}`,
      `${plan1Name} vs ${plan2Name}`,
      `Difference between ${plan1Name} ${plan2Name}`,
      `which is best ${plan1Name} ${plan2Name}`,
      `Compare ${plan1Name} ${plan2Name} health insurance`,
      `${plan1Name} vs ${plan2Name} health insurance`,
      `Difference between ${plan1Name} ${plan2Name} health insurance`,
      `which is best ${plan1Name} ${plan2Name} health insurance`,
      `Compare ${plan1Name} ${plan2Name} insurance plans`,
      `${plan1Name} vs ${plan2Name} insurance plans`,
      `Difference between ${plan1Name} ${plan2Name} insurance plans`,
      `which is best ${plan1Name} ${plan2Name} insurance plans`,
      `Compare ${plan1Name} ${plan2Name} medical insurance`,
      `${plan1Name} vs ${plan2Name} medical insurance`,
      `Difference between ${plan1Name} ${plan2Name} medical insurance`,
      `which is best ${plan1Name} ${plan2Name} medical insurance`,
    ];

    return {
      title,
      description,
      keywords: comparisonKeywords.join(", "),
      // robots: "noindex,nofollow",
      openGraph: {
        title,
        description,
        type: "website",
        url: pageUrl,
        images: [
          {
            url: imageUrl,
            width: 1200,
            height: 630,
            alt: title,
          },
        ],
        siteName: "OneAssure",
      },
      twitter: {
        card: "summary_large_image",
        title,
        description,
        images: [imageUrl],
        domain: "oneassure.in",
        url: pageUrl,
      },
      metadataBase: new URL("https://www.oneassure.in"),
      alternates: {
        canonical: `/compare-health-insurance-plans/${params.slugs}/${params.ids}`,
      },
      other: {
        // Facebook Meta Tags
        "og:url": pageUrl,
        "og:type": "website",
        "og:title": title,
        "og:description": description,
        "og:image": imageUrl,

        // Twitter Meta Tags
        "twitter:card": "summary_large_image",
        "twitter:domain": "oneassure.in",
        "twitter:url": pageUrl,
        "twitter:title": title,
        "twitter:description": description,
        "twitter:image": imageUrl,
      },
    };
  } catch (error) {
    // Fallback metadata
    return {
      title: "Compare Health Insurance Plans - Oneassure",
      description:
        "Compare Health Insurance Plans at Oneassure.in . Get the best coverage, benefits, premiums, topup plans & more tailored to your needs for Family, Indivisuals & Senior Citizen.",
    };
  }
}

async function fetchCompanyData(ids: string[]) {
  try {
    // Validate that ids is a non-empty array
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      throw new Error("Invalid ids parameter: must be a non-empty array");
    }

    // Filter out any null, undefined, or empty string values
    const validIds = ids.filter(
      (id) => id && typeof id === "string" && id.trim() !== ""
    );

    if (validIds.length === 0) {
      throw new Error("No valid IDs found in the ids array");
    }

    const query = `query MyQuery($ids: [String!]) { health_product_variants(where: {id:{ _in: $ids}}) { id variant_slug variant_name product { policy_brochure_url policy_wording_url name insurer { claim_settlement_ratio logo_url name network_hospital_count network_hospital_url slug } } feature_values { value metadata sub_value compare_feature { name sequence hint_text } } health_variant_static_content { best_for comparison_enabled decision_guide specialty subtitle id product_popularity } } site_comparison_how_is_expert_consultation { title points id } site_comparison_why_choose_expert_consultation { logo_url title description id } site_comparison_health_faqs { answer id question } }`;
    const operationName = "MyQuery";
    const variables = {
      ids: validIds,
    };
    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_BASE_URL}/api/studio-cms`,
      {
        query,
        operationName,
        variables,
      }
    );

    // Enhanced data validation
    if (
      response.data &&
      response.data.payload &&
      response.data.payload.data.health_product_variants &&
      Array.isArray(response.data.payload.data.health_product_variants) &&
      response.data.payload.data.health_product_variants.length > 0
    ) {
      return response.data.payload;
    } else {
      throw new Error("API returned empty or invalid data structure");
    }
  } catch (error) {
    throw new Error(
      `Failed to fetch company data: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    );
  }
}

async function fetchAllInsurerData() {
  try {
    // Check if environment variables are available
    if (
      !process.env.NEXT_PUBLIC_STRAPI_BASEURL ||
      !process.env.NEXT_PUBLIC_STRAPI_TOKEN
    ) {
      return [];
    }

    const response = await apiClient.get(
      `${process.env.NEXT_PUBLIC_STRAPI_BASEURL}/api/companies?filters[category][$eq]=health-insurance&fields[0]=name&fields[1]=slug&fields[2]=category&populate[logo][fields][0]=url&populate[ratings][fields][0]=solvency&populate[ratings][fields][1]=icr&populate[ratings][fields][2]=growth`,
      {
        headers: {
          Authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_TOKEN}`,
        },
      }
    );

    // Enhanced data transformation with error handling
    const transformedData = response.data.data
      .map((item: any, index: number) => {
        try {
          const slug = item.attributes?.slug;
          const name = item.attributes?.name;
          const logoUrl = item.attributes?.logo?.data?.attributes?.url;

          if (!slug || !name) {
            return null;
          }

          return {
            company_slug: slug,
            company_name: name,
            logo_url: logoUrl || "",
          };
        } catch (error) {
          return null;
        }
      })
      .filter(Boolean); // Remove null items

    return transformedData;
  } catch (error) {
    // Return empty array instead of throwing
    return [];
  }
}

async function getBlogData() {
  try {
    // Check if environment variables are available
    if (
      !process.env.NEXT_PUBLIC_STRAPI_BASEURL ||
      !process.env.NEXT_PUBLIC_STRAPI_TOKEN
    ) {
      return {
        heading: "Latest Blog Posts",
        blogs: [],
      };
    }

    const headers = {
      Authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_TOKEN}`,
    };

    const res = await fetch(
      `${process.env.NEXT_PUBLIC_STRAPI_BASEURL}/api/blogs?filters[topBlog][$eq]=true&populate[Thumbnail][fields][0]=url&populate[category][fields][0]=name&populate[category][fields][1]=slug&populate[author][fields][0]=name&fields[0]=title&fields[1]=subtitle&fields[2]=createdAt&fields[3]=slug&populate[subCategory][fields][0]=name&populate[subCategory][fields][1]=slug&pagination[pageSize]=100&pagination[page]=1&sort[0]=createdAt:desc`,
      {
        headers,
        next: { revalidate: 0 }, // Force fresh data
      }
    );

    if (!res.ok) {
      throw new Error(`Blog API responded with status: ${res.status}`);
    }

    const data = await res.json();

    // Enhanced data transformation with error handling
    const transformedData = {
      heading: "Latest Blog Posts",
      blogs: data.data
        .map((blog: any, index: number) => {
          try {
            return {
              title: blog.attributes?.Title || `Blog Post ${index + 1}`,
              date: blog.attributes?.createdAt
                ? new Date(blog.attributes.createdAt).toLocaleDateString(
                    "en-US",
                    {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    }
                  )
                : "Recent",
              author:
                blog.attributes?.author?.data?.attributes?.name ||
                "OneAssure Team",
              description: blog.attributes?.subtitle || "",
              imageUrl: blog.attributes?.Thumbnail?.data?.attributes?.url || "",
              url: `/insurance/${
                blog.attributes?.category?.data?.attributes?.slug || "blog"
              }/${blog.attributes?.slug || ""}`,
            };
          } catch (error) {
            return null;
          }
        })
        .filter(Boolean)
        .slice(0, 6), // Limit to 6 blogs and remove null items
    };

    return transformedData;
  } catch (error) {
    // Return fallback data instead of throwing
    return {
      heading: "Latest Blog Posts",
      blogs: [],
    };
  }
}

export default async function ComparePageWrapper({
  params,
}: {
  params: { slugs: string; ids: string };
}) {
  try {
    if (!params.ids || !params.ids.includes("-")) {
      throw new Error("Invalid ID format. Expected format: id1-id2");
    }

    // Split the IDs by "-" to get individual IDs
    const individualIds = params.ids.split("-");

    if (individualIds.length !== 2) {
      throw new Error(
        "Invalid ID format. Expected exactly two IDs to compare."
      );
    }

    // Sequential data fetching for better reliability

    // Fetch company data first (most critical) using IDs
    let data = null;
    try {
      data = await fetchCompanyData(individualIds);
    } catch (error) {
      data = null;
    }

    // Get popular plans (synchronous)
    const popularPlans = getPopularPlansData();

    // Fetch insurer data
    let allInsurerData = [];
    try {
      allInsurerData = await fetchAllInsurerData();
    } catch (error) {
      allInsurerData = [];
    }

    // Fetch blog data
    let blogData = { heading: "Latest Blog Posts", blogs: [] };
    try {
      blogData = await getBlogData();
    } catch (error) {
      blogData = { heading: "Latest Blog Posts", blogs: [] };
    }

    const breadcrumbs = [
      { name: "Oneassure", item: "https://www.oneassure.in/" },
      {
        name: "Health Insurance",
        item: "https://www.oneassure.in/health-insurance",
      },
      {
        name: "Compare Insurance",
        item: "https://www.oneassure.in/compare-health-insurance-plans",
      },
    ];

    return (
      <>
        <BreadcrumbsSchema breadcrumbs={breadcrumbs} />
        <FaqSchema
          faqs={data?.data?.site_comparison_health_faqs?.map(
            (variant: { question: string; answer: string }) => ({
              question: variant?.question,
              answer: variant?.answer,
            })
          )}
        />
        <ComparisonPage
          data={data?.data}
          popularPlans={popularPlans}
          allInsurerData={allInsurerData}
          blogData={blogData}
        />
      </>
    );
  } catch (error) {
    // Return a fallback component or error page
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">
            Comparison Error
          </h1>
          <p className="text-gray-600 mb-4">
            {error instanceof Error
              ? error.message
              : "An unexpected error occurred while loading the comparison."}
          </p>
          <a
            href="/compare-health-insurance-plans"
            className="text-blue-600 hover:text-blue-800 underline"
          >
            Back to Health Insurance Comparisons
          </a>
        </div>
      </div>
    );
  }
}
