

import parse, { DOMN<PERSON>, domToReact, Element } from "html-react-parser";
import Link from "next/link";
import { ReactElement, ComponentType } from "react";

type ComponentMap = {
  p?: ComponentType<any>;
  h1?: ComponentType<any>;
  h2?: ComponentType<any>;
  h3?: ComponentType<any>;
  h4?: ComponentType<any>;
  h5?: ComponentType<any>;
  h6?: ComponentType<any>;
  span?: ComponentType<any>;
  a?: ComponentType<any>;
  strong?: ComponentType<any>;
  ul?: ComponentType<any>;
  ol?: ComponentType<any>;
  li?: ComponentType<any>;
  img?: ComponentType<any>;
  iframe?: ComponentType<any>;
  table?: ComponentType<any>;
  tr?: ComponentType<any>;
  td?: ComponentType<any>;
  th?: ComponentType<any>;
}

type ClassMap = {
  p?: string;
  h1?: string;
  h2?: string;
  h3?: string;
  h4?: string;
  h5?: string;
  h6?: string;
  span?: string;
  a?: string;
  strong?: string;
  ul?: string;
  ol?: string;
  li?: string;
  img?: string;
  iframe?: string;
  table?: string;
  tr?: string;
  td?: string;
  th?: string;
  heading?: string;
  body?: string;
  headingSpan?: string;
  bodySpan?: string;
}

export type  HtmlParserOptions = {
  components: ComponentMap;
  className?: string;
  classNames?: ClassMap;
  stripStyles?: boolean;
  imageProps?: {
    fill?: boolean;
    style?: React.CSSProperties;
    className?: string;
  };
}

export const htmlParser = (html: string, options: HtmlParserOptions): ReactElement => {
  const { components, className = "", classNames = {}, stripStyles = true, imageProps } = options;

  const htmlRenderer = (domNode: DOMNode) => {
    if (domNode.type === "tag" && domNode.attribs) {
      let attrs = domNode.attribs;
      
      if (stripStyles && attrs.style) {
        attrs.style = "";
      }

      const Component = components[domNode.name as keyof ComponentMap];
      const tagClassName = classNames[domNode.name as keyof ClassMap] || className;
      const headingClassName = classNames.heading;
      const bodyClassName = classNames.body;
      const headingSpanClassName = classNames.headingSpan;
      const bodySpanClassName = classNames.bodySpan;
      
      if (Component) {
        // Special handling for images
        if (domNode.name === "img" && attrs.src) {
          return (
            <Component
              src={attrs.src}
              alt={attrs.alt || "image"}
              fill={imageProps?.fill || true}
              style={imageProps?.style || { objectFit: "contain" }}
              className={imageProps?.className || tagClassName}
            />
          );
        }

        // Handle p with span extraction
        if (domNode.name === "p" && domNode.children?.length > 0) {
          const spanChild = domNode.children.find(
            (child): child is Element =>
              "name" in child && child.name === "span"
          );

          if (spanChild) {
            return (
              <Component as="p" className={bodySpanClassName || bodyClassName || tagClassName}>
                {domToReact(spanChild.children as DOMNode[])}
              </Component>
            );
          }
        }

        // Handle headings with span extraction
        if ((domNode.name === "h1" || domNode.name === "h2" || domNode.name === "h3" || domNode.name === "h4" || domNode.name === "h5" || domNode.name === "h6") && domNode.children?.length > 0) {
          const spanChild = domNode.children.find(
            (child): child is Element =>
              "name" in child && child.name === "span"
          );

          if (spanChild) {
            return (
              <Component as={domNode.name} className={headingSpanClassName || headingClassName || tagClassName}>
                {domToReact(spanChild.children as DOMNode[])}
              </Component>
            );
          }
        }

        // Handle links
        if (domNode.name === "a" && attrs.href) {
          return (
            <Link href={attrs.href} className={tagClassName}>
              {domToReact(domNode.children as DOMNode[])}
            </Link>
          );
        }

        // Default handling for other elements
        return (
          <Component as={domNode.name} className={tagClassName || bodyClassName}>
            {domToReact(domNode.children as DOMNode[])}
          </Component>
        );
      }
    }
    return domNode;
  };

  return <>{parse(html, { replace: htmlRenderer })}</>;
};
