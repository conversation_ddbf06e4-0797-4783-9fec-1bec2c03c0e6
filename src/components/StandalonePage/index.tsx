"use client";
import { StandalonePageData } from "./dto";
import Hero from "../Insurer/component/Hero";
import ExpertReview from "../Insurer/component/ExpertReview";
import PlansSection from "../globals/DSComponentsV0/PlansSection";
import Testimonial from "@/components/globals/Testimonial";
import CardContainer from "./components/CardContainer";
import InclusionExclusionSection from "./components/InclusionExclusionSection";
import ClaimTypes from "../Insurer/component/ClaimTypes";
import RenewalTypes from "../Insurer/component/RenewalTypes";
import CategoryCards from "../globals/CategoryCards";
import LeadForm from "../globals/LeadForm";
import FAQs from "../globals/AccordianSection";
import GoToTopFloater from "../globals/GoToTopFloater";
import CalendlyFloater from "../globals/CalendlyFloater";
import SharePageFloater from "../globals/SharePageFloater";
import InsurerPlan from "../globals/InsurerPlan";
import RelatedBlogs from "../globals/RelatedBlogs";
import PageNavigation from "../globals/PageNavigation";
import { useState } from "react";

const Standalone = ({
  data,
  allInsurerData,
  blogData,
}: {
  data: StandalonePageData;
  allInsurerData?: any[];
  blogData?: {
    heading: string;
    blogs: Array<{
      title: string;
      date: string;
      author: string;
      description: string;
      imageUrl: string;
      url: string;
    }>;
  };
}) => {
  const [activeTab, setActiveTab] = useState("expert-review");
  if (!data) {
    return null;
  }
  return (
    <>
      <Hero
        name={data.heroSection.title}
        description={data.heroSection.description}
        image={data.heroSection.image}
        slug={data.slug}
        breadcrumbPath={data.heroSection.breadcrumbPath}
        stats={data.heroSection.stats}
      />
      <PageNavigation
        activeTab={data.pageNavigationSection.activeTab}
        setActiveTab={setActiveTab}
        tabs={data.pageNavigationSection.tabs}
      />
      <ExpertReview
        pill={data.verdictSection.pill}
        heading={data.verdictSection.title}
        subheading={data.verdictSection.description}
        whatWeLike={{
          heading: "Pros",
          points: data.verdictSection.prosCons
            .filter((point) => point.type === "pro")
            .map((point) => point.points),
        }}
        AreasOfImprovement={{
          heading: "Cons",
          points: data.verdictSection.prosCons
            .filter((point) => point.type === "con")
            .map((point) => point.points),
        }}
        verdict={data.verdictSection.verdict}
      />
      <PlansSection
        heading={data.plansSection.title}
        subheading={data.plansSection.description}
        pill={data.plansSection.pill}
        plans={data.plansSection.plans}
        id="plans"
      />
      <Testimonial
        testimonials={data.testimonialSection.points}
        sectionHeaderProps={{
          pill: data.testimonialSection.pill,
          heading: data.testimonialSection.title,
          subheading: data.testimonialSection.description,
        }}
        pill={data.testimonialSection.pill}
      />

      {/* Why Choose This Plan */}
      <CardContainer
        heading={data.whyPlansSection.title}
        subheading={data.whyPlansSection.description}
        pill={data.whyPlansSection.pill}
        card_content={data.whyPlansSection.points}
        grid_cols={3}
        id="why-choose-this-plan"
      />

      {/* Benefits Of This Plan */}
      <CardContainer
        heading={data.benefitsSection.title}
        subheading={data.benefitsSection.description}
        pill={data.benefitsSection.pill}
        card_content={data.benefitsSection.points}
        grid_cols={3}
        id="benefits-of-this-plan"
      />

      {/* Inclusions And Exclusions */}
      <InclusionExclusionSection
        heading={data.inclusionSection.title}
        subheading={data.inclusionSection.description}
        pill={data.inclusionSection.pill}
        inclusions={data.inclusionSection.inclusions}
        exclusions={data.inclusionSection.exclusions}
        id="inclusions-and-exclusions"
      />

      {/* What To Look For */}
      <CardContainer
        heading={data.whatToLookForSection.title}
        subheading={data.whatToLookForSection.description}
        pill={data.whatToLookForSection.pill}
        card_content={data.whatToLookForSection.points}
        grid_cols={4}
        id="what-to-look-for"
      />

      {/* Key Factors */}
      <CardContainer
        heading={data.keyFactorsSection.title}
        subheading={data.keyFactorsSection.description}
        pill={data.keyFactorsSection.pill}
        card_content={data.keyFactorsSection.points}
        grid_cols={4}
        id="key-factors"
      />

      {/* Tax Advantage */}
      <CardContainer
        heading={data.taxAdvantageSection.title}
        subheading={data.taxAdvantageSection.description}
        pill={data.taxAdvantageSection.pill}
        card_content={data.taxAdvantageSection.points}
        grid_cols={4}
        id="tax-advantage"
      />

      {/* Documents Required */}
      <CardContainer
        heading={data.documentsSection.title}
        subheading={data.documentsSection.description}
        pill={data.documentsSection.pill}
        card_content={data.documentsSection.points}
        grid_cols={4}
        id="documents-required"
      />

      {/* Claim Settlement Process */}
      <ClaimTypes
        heading={data.claimSettlementSection.title}
        subheading={data.claimSettlementSection.description}
        pill={data.claimSettlementSection.pill}
        claimSettlements={data.claimSettlementSection.settlements}
      />

      {/* Renewal Process */}
      <RenewalTypes
        heading={data.renewalSection.title}
        subheading={data.renewalSection.description}
        pill={data.renewalSection.pill}
        renewalSteps={data.renewalSection.renewalSteps}
      />

      {/* Insurance Category */}
      <CategoryCards
        pill={data.insuranceCategorySection.pill}
        heading={data.insuranceCategorySection.title}
        subHeading={data.insuranceCategorySection.description}
        categories={data.insuranceCategorySection.cards}
        id="insurance-category"
      />

      {/* Lead Form */}
      <LeadForm
        pill="Plan Listing"
        title="Still Confused? Get Expert Guidance"
        description="Our insurance experts can help you compare plans and find the best coverage for your needs"
      />

      {/* FAQ Section */}
      <FAQs
        pill={data.faqSection.pill}
        heading={data.faqSection.title}
        subheading={data.faqSection.description}
        faqs={data.faqSection.faqs}
        id="faqs"
      />

      {/* Insurer Plans */}
      {allInsurerData && allInsurerData.length > 0 && (
        <InsurerPlan allInsurerData={allInsurerData} />
      )}
      {/* Related Blogs */}
      {blogData && blogData.blogs.length > 0 && (
        <RelatedBlogs blogData={blogData} />
      )}
      <GoToTopFloater />
      <CalendlyFloater />
      <SharePageFloater />
    </>
  );
};

export default Standalone;
